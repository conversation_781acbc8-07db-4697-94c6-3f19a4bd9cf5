'use client';

import { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  UserPlus, 
  Activity, 
  Clock, 
  Award,
  TrendingUp,
  Calendar,
  Mail,
  Phone
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function TeamPage() {
  // Demo team data
  const teamMembers = [
    {
      id: 1,
      name: 'أحمد محمد',
      role: 'developer',
      email: '<EMAIL>',
      phone: '+20 ************',
      avatar: null,
      status: 'active',
      productivity: 92,
      tasksCompleted: 24,
      currentProject: 'موقع شركة التقنية',
      joinDate: '2023-01-15'
    },
    {
      id: 2,
      name: 'فاطمة علي',
      role: 'designer',
      email: '<EMAIL>',
      phone: '+20 ************',
      avatar: null,
      status: 'active',
      productivity: 88,
      tasksCompleted: 18,
      currentProject: 'تطبيق التجارة الإلكترونية',
      joinDate: '2023-02-20'
    },
    {
      id: 3,
      name: 'محمد حسن',
      role: 'media_buyer',
      email: '<EMAIL>',
      phone: '+20 ************',
      avatar: null,
      status: 'active',
      productivity: 95,
      tasksCompleted: 32,
      currentProject: 'حملة إعلانية للمطعم',
      joinDate: '2023-03-10'
    },
    {
      id: 4,
      name: 'سارة أحمد',
      role: 'wordpress_developer',
      email: '<EMAIL>',
      phone: '+20 ************',
      avatar: null,
      status: 'active',
      productivity: 85,
      tasksCompleted: 15,
      currentProject: 'موقع المطعم',
      joinDate: '2023-04-05'
    }
  ];

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: 'مدير النظام',
      sales_manager: 'مدير المبيعات',
      media_buyer: 'مشتري الإعلانات',
      developer: 'مطور',
      designer: 'مصمم',
      wordpress_developer: 'مطور ووردبريس',
    };
    return roleMap[role] || role;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'on_leave': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.SALES_MANAGER]}>
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة الفريق</h1>
              <p className="text-gray-600 mt-1">إدارة أعضاء الفريق ومتابعة الأداء</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                قيد التطوير
              </Badge>
              <Button>
                <UserPlus className="h-4 w-4 ml-2" />
                إضافة عضو جديد
              </Button>
            </div>
          </div>

          {/* Team Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الأعضاء</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMembers.length}</div>
                <p className="text-xs text-muted-foreground">
                  +1 من الشهر الماضي
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متوسط الإنتاجية</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">90%</div>
                <p className="text-xs text-muted-foreground">
                  +5% من الأسبوع الماضي
                </p>
                <Progress value={90} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المهام المكتملة</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">89</div>
                <p className="text-xs text-muted-foreground">
                  هذا الأسبوع
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">ساعات العمل</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">160</div>
                <p className="text-xs text-muted-foreground">
                  هذا الأسبوع
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Team Members */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                أعضاء الفريق
              </CardTitle>
              <CardDescription>
                قائمة بجميع أعضاء الفريق ومعلوماتهم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                        {member.name.charAt(0)}
                      </div>
                      <div>
                        <h4 className="font-medium text-lg">{member.name}</h4>
                        <p className="text-sm text-gray-600">{getRoleDisplayName(member.role)}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Mail className="h-3 w-3" />
                            {member.email}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Phone className="h-3 w-3" />
                            {member.phone}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{member.productivity}%</div>
                        <p className="text-xs text-gray-500">الإنتاجية</p>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{member.tasksCompleted}</div>
                        <p className="text-xs text-gray-500">مهمة مكتملة</p>
                      </div>
                      <div className="text-center">
                        <Badge className={getStatusColor(member.status)}>
                          {member.status === 'active' && 'نشط'}
                          {member.status === 'inactive' && 'غير نشط'}
                          {member.status === 'on_leave' && 'في إجازة'}
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        عرض التفاصيل
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Development Notice */}
          <Card className="mt-8 border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900">قيد التطوير</h3>
                  <p className="text-blue-700 text-sm">
                    هذه الصفحة قيد التطوير. سيتم إضافة المزيد من الميزات قريباً مثل إدارة الأذونات، تتبع الحضور، وتقييم الأداء.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    </RoleGuard>
  );
}
